"""
Pydantic schemas for Luma Labs video generation API.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from enum import Enum


class ResolutionEnum(str, Enum):
    """Supported video resolutions."""
    SD_540P = "540p"
    HD_720P = "720p"
    FULL_HD_1080P = "1080p"
    ULTRA_HD_4K = "4k"


class AspectRatioEnum(str, Enum):
    """Supported aspect ratios."""
    WIDESCREEN = "16:9"
    SQUARE = "1:1"
    VERTICAL = "9:16"


class ConceptEnum(str, Enum):
    """Special video concepts/effects."""
    DOLLY_ZOOM = "dolly_zoom"


class LumaTextToVideoRequest(BaseModel):
    """Request model for text-to-video generation."""
    prompt: str = Field(..., description="Text description of the video to create")
    resolution: ResolutionEnum = Field(default=ResolutionEnum.HD_720P, description="Video resolution")
    duration: int = Field(default=5, description="Video duration in seconds (5 or 9 only)")
    aspect_ratio: AspectRatioEnum = Field(default=AspectRatioEnum.WIDESCREEN, description="Video aspect ratio")
    loop: bool = Field(default=False, description="Whether the video should loop seamlessly")
    concepts: Optional[List[ConceptEnum]] = Field(default=None, description="Special effects to apply")

    def validate_duration(self):
        """Validate that duration is 5 or 9 seconds."""
        if self.duration not in [5, 9]:
            raise ValueError("Duration must be 5 or 9 seconds")


class LumaImageToVideoRequest(BaseModel):
    """Request model for image-to-video generation."""
    prompt: str = Field(..., description="Text description of the video to create")
    resolution: ResolutionEnum = Field(default=ResolutionEnum.HD_720P, description="Video resolution")
    duration: int = Field(default=5, description="Video duration in seconds (5 or 9 only)")
    aspect_ratio: AspectRatioEnum = Field(default=AspectRatioEnum.WIDESCREEN, description="Video aspect ratio")
    loop: bool = Field(default=False, description="Whether the video should loop seamlessly")
    keyframe_type: str = Field(..., description="Type of keyframes: 'frame0', 'frame1', or 'both'")

    def validate_duration(self):
        """Validate that duration is 5 or 9 seconds."""
        if self.duration not in [5, 9]:
            raise ValueError("Duration must be 5 or 9 seconds")


class LumaVideoMetadata(BaseModel):
    """Metadata for generated videos."""
    model: str
    resolution: str
    duration: int
    aspect_ratio: str
    loop: bool
    original_prompt: str
    enhanced_prompt: str
    generation_type: str
    concepts: Optional[List[str]] = None
    keyframes: Optional[Dict[str, str]] = None


class LumaVideoResponse(BaseModel):
    """Response model for video generation."""
    success: bool
    video_url: Optional[str] = None
    generation_id: Optional[str] = None
    status: Optional[str] = None
    metadata: Optional[LumaVideoMetadata] = None
    error: Optional[str] = None


class FrontendLumaResponse(BaseModel):
    """Frontend-compatible response model."""
    success: bool
    video_url: Optional[str] = None
    generation_id: Optional[str] = None
    status: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    @classmethod
    def from_service_response(cls, service_response: Dict[str, Any]) -> "FrontendLumaResponse":
        """Convert service response to frontend response."""
        return cls(
            success=service_response.get("success", False),
            video_url=service_response.get("video_url"),
            generation_id=service_response.get("generation_id"),
            status=service_response.get("status"),
            metadata=service_response.get("metadata"),
            error=service_response.get("error")
        )


class LumaGenerationStatusRequest(BaseModel):
    """Request model for checking generation status."""
    generation_id: str = Field(..., description="ID of the generation to check")


class LumaGenerationStatusResponse(BaseModel):
    """Response model for generation status."""
    success: bool
    generation_id: str
    status: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class LumaStreamResponse(BaseModel):
    """Response model for streaming updates."""
    success: bool
    partial_video: bool = False
    progress: int = 0
    message: str = ""
    video_url: Optional[str] = None
    generation_id: Optional[str] = None
    error: Optional[str] = None


class FrontendStreamResponse(BaseModel):
    """Frontend-compatible streaming response."""
    success: bool
    partial_video: bool = False
    progress: int = 0
    message: str = ""
    video_url: Optional[str] = None
    generation_id: Optional[str] = None
    error: Optional[str] = None

    @classmethod
    def from_service_response(cls, service_response: Dict[str, Any]) -> "FrontendStreamResponse":
        """Convert service response to frontend streaming response."""
        return cls(
            success=service_response.get("success", False),
            partial_video=service_response.get("partial_video", False),
            progress=service_response.get("progress", 0),
            message=service_response.get("message", ""),
            video_url=service_response.get("video_url"),
            generation_id=service_response.get("generation_id"),
            error=service_response.get("error")
        )
