import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@shared": path.resolve(__dirname, "../shared"),
    },
  },
  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "lucide-react",
      "classnames",
      "prop-types",
      "mobx-react-lite",
      "mobx"
    ]
  },
  build: {
    outDir: "dist",
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          // Polotno (largest chunk - 4.2MB)
          'polotno': ['polotno'],

          // React ecosystem
          'react-vendor': ['react', 'react-dom'],

          // UI libraries
          'ui-vendor': ['framer-motion', 'lucide-react'],

          // Query and routing
          'query-vendor': ['@tanstack/react-query', 'wouter']
        }
      }
    }
  },
  server: {
    host: true,
    port: 3002, // Frontend port (Emma Studio custom)
    strictPort: false, // Allow Vite to find an available port
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000', // Backend server correcto (puerto 8000)
        changeOrigin: true,
        secure: false, // Allow insecure connections for development
        // Enhanced logging and debugging for proxy
        rewrite: (path) => {
          // Log the path for debugging
          console.log(`Proxying request: ${path}`);
          return path;
        },
        configure: (proxy, options) => {
          // Add error handling for proxy
          proxy.on('error', (err, req, res) => {
            console.error('Proxy error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Proxy request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Proxy response:', proxyRes.statusCode, req.url);
          });
        }
      },
    },
  },
});
